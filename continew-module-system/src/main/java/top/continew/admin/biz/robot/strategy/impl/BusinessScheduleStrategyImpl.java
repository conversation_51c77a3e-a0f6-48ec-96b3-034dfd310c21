package top.continew.admin.biz.robot.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.enums.ScheduleTypeEnum;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.req.RobotScheduleReq;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.SalesScheduleService;
import top.continew.admin.system.model.entity.UserDO;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessScheduleStrategyImpl implements RobotCommandStrategy {

    private final SalesScheduleService salesScheduleService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.BUSINESS_SCHEDULE;
    }

    @Override
    public String execute(Update update) {
        RobotScheduleReq req = BotUtils.parseBotMsgToObject(update.getMessage().getText(), "\n", ":", RobotScheduleReq.class);
        log.info("{}收到一条排班请求：{}", getCommand().getDescription(), JSONObject.toJSONString(req));
        LocalDate date;
        try {
            date = LocalDate.parse(req.getDate());
        } catch (DateTimeParseException e) {
            return "❌ 日期格式错误，示例2025-03-22";
        }
        String[] personnel = req.getPersonnel().trim().split(" ");
        List<String> personnelList = new ArrayList<>();
        for (String s : personnel) {
            if (StringUtils.isNotBlank(s)) {
                personnelList.add(s.substring(1));
            }
        }
        List<UserDO> userDOS = salesScheduleService.getUserListByTelegramId(personnelList);
        if (userDOS.size() != personnelList.size()) {
            return "❌ 人员信息错误，请检查";
        }

        List<SalesScheduleDO> saveList = new ArrayList<>();
        for (UserDO userDO : userDOS) {
            SalesScheduleDO salesScheduleDO = new SalesScheduleDO();
            salesScheduleDO.setScheduleDate(date);
            salesScheduleDO.setScheduleType(ScheduleTypeEnum.SCHEDULE);
            salesScheduleDO.setSalesId(userDO.getId());
            saveList.add(salesScheduleDO);
        }
        salesScheduleService.telegramSaveBatch(saveList);
        return "✅ 排班信息提交成功！";
    }
}
