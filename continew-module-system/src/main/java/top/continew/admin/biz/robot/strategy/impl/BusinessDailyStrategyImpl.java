package top.continew.admin.biz.robot.strategy.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.SalesDailySummaryService;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessDailyStrategyImpl implements RobotCommandStrategy {

    private final SalesDailySummaryService salesDailySummaryService;

    private final UserService userService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.BUSINESS_DAILY;
    }

    @Override
    public String execute(Update update) {


        String userName = update.getMessage().getFrom().getUserName();

        UserDO user = userService.selectByTelegramId(userName);

        if (user == null) {
            return "❌ 没找到对应的用户，请联系管理员";
        }

        SalesDailySummaryDO salesDailySummary = BotUtils.parseDailyReportToObject(update.getMessage().getText());
        salesDailySummary.setCreateUser(user.getId());

        try {
            salesDailySummaryService.telegramSave(salesDailySummary);
            return "✅ 商务日报提交成功！";
        } catch (Exception e) {
            log.error("保存日报失败", e);
            return "❌ 日报保存失败，请稍后重试";
        }
    }
}
