package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;


import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商务人员排班/请假记录参数
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@Schema(description = "创建或修改商务人员排班/请假记录参数")
public class SalesScheduleReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;
}