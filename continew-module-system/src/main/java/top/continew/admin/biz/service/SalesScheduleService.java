package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;

import java.util.List;

/**
 * 商务人员排班/请假记录业务接口
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
public interface SalesScheduleService extends BaseService<SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq>, IService<SalesScheduleDO> {

    List<UserDO> getUserListByTelegramId(List<String> telegramIds);

}