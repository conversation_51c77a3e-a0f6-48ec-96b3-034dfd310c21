package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;

import java.util.List;

/**
 * 商务人员排班/请假记录业务接口
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
public interface SalesScheduleService extends BaseService<SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq>, IService<SalesScheduleDO> {

    List<UserDO> getUserListByTelegramId(List<String> telegramIds);

    /**
     * Telegram机器人批量保存排班信息（支持覆盖）
     * 如果存在相同的记录（基于salesId + scheduleDate + scheduleType），则更新；否则新增
     *
     * @param scheduleList 排班信息列表
     */
    void telegramSaveBatch(List<SalesScheduleDO> scheduleList);

    void telegramSave(SalesScheduleDO salesSchedule);
}