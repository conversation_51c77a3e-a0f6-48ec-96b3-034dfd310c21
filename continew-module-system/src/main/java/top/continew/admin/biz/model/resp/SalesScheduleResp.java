package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 商务人员排班/请假记录信息
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@Schema(description = "商务人员排班/请假记录信息")
public class SalesScheduleResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商务人员ID
     */
    @Schema(description = "商务人员ID")
    private Long salesId;

    /**
     * 排班/请假的具体日期
     */
    @Schema(description = "排班/请假的具体日期")
    private LocalDate scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    @Schema(description = "类型: 1=请假, 2=排班")
    private Integer scheduleType;
}