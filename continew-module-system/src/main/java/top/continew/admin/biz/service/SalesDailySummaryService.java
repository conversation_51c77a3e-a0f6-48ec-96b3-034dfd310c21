package top.continew.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.SalesDailySummaryQuery;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.model.resp.SalesDailySummaryDetailResp;
import top.continew.admin.biz.model.resp.SalesDailySummaryResp;

/**
 * 商务日报业务接口
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
public interface SalesDailySummaryService extends BaseService<SalesDailySummaryResp, SalesDailySummaryDetailResp, SalesDailySummaryQuery, SalesDailySummaryReq>, IService<SalesDailySummaryDO> {

    void telegramSave(SalesDailySummaryDO salesDailySummaryDO);

    Long findUser(String userName);

}