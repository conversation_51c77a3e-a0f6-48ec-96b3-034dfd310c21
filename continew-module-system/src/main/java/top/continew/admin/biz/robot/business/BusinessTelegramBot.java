package top.continew.admin.biz.robot.business;

/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


import cn.hutool.extra.spring.SpringUtil;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Chat;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;
import top.continew.admin.biz.event.BusinessTelegramMessageEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategyFactory;

@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessTelegramBot extends TelegramLongPollingBot {

    @Value("${businessTelegram.token}")
    private String token;

    @Value("${businessTelegram.username}")
    private String username;

    @Getter
    @Value("${businessTelegram.chatId}")
    private Long chatId;

    private final RobotCommandStrategyFactory robotCommandStrategyFactory;


    @Override
    public String getBotUsername() {
        return username;
    }

    @PostConstruct
    public void init() {
        TelegramBotsApi telegramBotsApi = null;
        try {
            telegramBotsApi = new TelegramBotsApi(DefaultBotSession.class);
            telegramBotsApi.registerBot(this);
            log.info("商务TG机器人启动成功");
        } catch (TelegramApiException e) {
            log.error("商务TG机器人启动失败：{}", e.getMessage());
        }
    }

    @Override
    public String getBotToken() {
        return token;
    }

    @Override
    public void onUpdateReceived(Update update) {
        if (update.hasMessage()) {
            if (update.getMessage().hasText()) {
                Chat chat = update.getMessage().getChat();
                Message message = update.getMessage();
                log.info("【TELEGRAM】[{}]-[{}]发送消息：{}", chat.getTitle(), message.getFrom().getFirstName(), message.getText());
                if (chat.getId().equals(chatId)) {
                    RobotCommandEnum command = getCommand(message.getText());
                    RobotCommandStrategy strategy = robotCommandStrategyFactory.findStrategy(command);
                    // 日报
                    if (strategy != null && strategy.getCommand() == RobotCommandEnum.BUSINESS_DAILY) {
                        String msg = strategy.execute(update);
                        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                                .chatId(update.getMessage().getChatId())
                                .replyToMessageId(message.getMessageId())
                                .text(msg)
                                .build()));
                    }
                    // 排班
                    if (strategy != null && strategy.getCommand() == RobotCommandEnum.BUSINESS_SCHEDULE) {
                        String msg = strategy.execute(update);
                        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                                .chatId(update.getMessage().getChatId())
                                .replyToMessageId(message.getMessageId())
                                .text(msg)
                                .build()));
                    }
                    // 请假
                    if (strategy != null && strategy.getCommand() == RobotCommandEnum.BUSINESS_LEAVE) {
                        String msg = strategy.execute(update);
                        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                                .chatId(update.getMessage().getChatId())
                                .replyToMessageId(message.getMessageId())
                                .text(msg)
                                .build()));
                    }

                }
            }
        }
    }

    private RobotCommandEnum getCommand(String text) {
        String firstLine = StringUtils.substringBefore(text, "\n");
        for (RobotCommandEnum value : RobotCommandEnum.values()) {
            if (value.getDescription().equals(firstLine)) {
                return value;
            }
        }
        return RobotCommandEnum.UNKNOWN;
    }
}
