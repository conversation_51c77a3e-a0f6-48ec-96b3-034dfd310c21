package top.continew.admin.biz.robot.strategy.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.system.mapper.UserMapper;
import top.continew.admin.system.model.entity.UserDO;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BusinessLeaveStrategyImpl implements RobotCommandStrategy {

    private final UserMapper userMapper;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.BUSINESS_LEAVE;
    }

    @Override
    public String execute(Update update) {

        List<UserDO> userDOS = userMapper.selectList(null);
        for (UserDO userDO : userDOS) {
            System.out.println(userDO);
        }

        return "";
    }
}
