package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 商务人员排班/请假记录查询条件
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@Schema(description = "商务人员排班/请假记录查询条件")
public class SalesScheduleQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商务人员ID
     */
    @Schema(description = "商务人员ID")
    @Query(type = QueryType.EQ)
    private Long salesId;

    /**
     * 排班/请假的具体日期
     */
    @Schema(description = "排班/请假的具体日期")
    @Query(type = QueryType.BETWEEN)
    private LocalDate[] scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    @Schema(description = "类型: 1=请假, 2=排班")
    @Query(type = QueryType.EQ)
    private Integer scheduleType;
}