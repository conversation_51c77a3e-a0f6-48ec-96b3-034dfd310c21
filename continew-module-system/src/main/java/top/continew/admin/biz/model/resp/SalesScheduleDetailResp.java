package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 商务人员排班/请假记录详情信息
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务人员排班/请假记录详情信息")
public class SalesScheduleDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商务人员ID
     */
    @Schema(description = "商务人员ID")
    @ExcelProperty(value = "商务人员ID")
    private Long salesId;

    /**
     * 排班/请假的具体日期
     */
    @Schema(description = "排班/请假的具体日期")
    @ExcelProperty(value = "排班/请假的具体日期")
    private LocalDate scheduleDate;

    /**
     * 类型: 1=请假, 2=排班
     */
    @Schema(description = "类型: 1=请假, 2=排班")
    @ExcelProperty(value = "类型: 1=请假, 2=排班")
    private Integer scheduleType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}