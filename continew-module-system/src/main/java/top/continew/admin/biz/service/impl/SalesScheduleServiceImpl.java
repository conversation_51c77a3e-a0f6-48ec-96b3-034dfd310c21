package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.SalesScheduleMapper;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;
import top.continew.admin.biz.service.SalesScheduleService;

import java.util.List;

/**
 * 商务人员排班/请假记录业务实现
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Service
@RequiredArgsConstructor
public class SalesScheduleServiceImpl extends BaseServiceImpl<SalesScheduleMapper, SalesScheduleDO, SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq> implements SalesScheduleService {



    @Override
    public List<UserDO> getUserListByTelegramId(List<String> telegramIds) {
        return baseMapper.getUserListByTelegramId(telegramIds);
    }
}