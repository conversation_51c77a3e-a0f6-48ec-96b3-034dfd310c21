package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import top.continew.admin.system.model.entity.UserDO;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.SalesScheduleMapper;
import top.continew.admin.biz.model.entity.SalesScheduleDO;
import top.continew.admin.biz.model.query.SalesScheduleQuery;
import top.continew.admin.biz.model.req.SalesScheduleReq;
import top.continew.admin.biz.model.resp.SalesScheduleDetailResp;
import top.continew.admin.biz.model.resp.SalesScheduleResp;
import top.continew.admin.biz.service.SalesScheduleService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商务人员排班/请假记录业务实现
 *
 * <AUTHOR>
 * @since 2025/08/04 11:56
 */
@Service
@RequiredArgsConstructor
public class SalesScheduleServiceImpl extends BaseServiceImpl<SalesScheduleMapper, SalesScheduleDO, SalesScheduleResp, SalesScheduleDetailResp, SalesScheduleQuery, SalesScheduleReq> implements SalesScheduleService {

    @Override
    public List<UserDO> getUserListByTelegramId(List<String> telegramIds) {
        return baseMapper.getUserListByTelegramId(telegramIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void telegramSaveBatch(List<SalesScheduleDO> scheduleList) {
        if (CollUtil.isEmpty(scheduleList)) {
            return;
        }

        // 批量查询现有记录，避免N+1查询问题
        List<SalesScheduleDO> existingRecords = lambdaQuery()
                .in(SalesScheduleDO::getSalesId, scheduleList.stream().map(SalesScheduleDO::getSalesId).distinct().collect(Collectors.toList()))
                .in(SalesScheduleDO::getScheduleDate, scheduleList.stream().map(SalesScheduleDO::getScheduleDate).distinct().collect(Collectors.toList()))
                .in(SalesScheduleDO::getScheduleType, scheduleList.stream().map(SalesScheduleDO::getScheduleType).distinct().collect(Collectors.toList()))
                .list();

        // 创建映射表，用于快速查找现有记录
        Map<String, SalesScheduleDO> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(
                        record -> generateKey(record.getSalesId(), record.getScheduleDate(), record.getScheduleType()),
                        record -> record
                ));

        // 处理每个要保存的记录
        for (SalesScheduleDO schedule : scheduleList) {
            String key = generateKey(schedule.getSalesId(), schedule.getScheduleDate(), schedule.getScheduleType());
            SalesScheduleDO existingRecord = existingMap.get(key);

            if (existingRecord != null) {
                // 存在旧记录，设置ID进行更新
                schedule.setId(existingRecord.getId());
            }
            // 如果不存在，则作为新记录插入（ID为null）
        }

        // 批量保存或更新
        saveOrUpdateBatch(scheduleList);
    }

    /**
     * 生成唯一键，用于标识排班记录
     */
    private String generateKey(Long salesId, java.time.LocalDate scheduleDate, top.continew.admin.biz.enums.ScheduleTypeEnum scheduleType) {
        return salesId + "_" + scheduleDate + "_" + scheduleType.getValue();
    }
}