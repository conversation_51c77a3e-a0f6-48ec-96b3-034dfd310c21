/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.common.util;

import lombok.extern.slf4j.Slf4j;
import top.continew.admin.common.context.UserContext;
import top.continew.admin.common.context.UserContextHolder;

/**
 * 非 Web 上下文工具类
 * 用于在定时任务、异步任务等非 Web 环境中处理用户上下文
 *
 * <AUTHOR> 4.0 sonnet
 * @since 2025/08/04
 */
@Slf4j
public class NonWebContextUtils {

    private NonWebContextUtils() {
    }

    /**
     * 在非 Web 上下文中执行需要用户身份的操作
     * 
     * @param userId 用户ID
     * @param task 要执行的任务
     */
    public static void executeWithUserContext(Long userId, Runnable task) {
        if (userId == null) {
            log.warn("用户ID为空，将以系统身份执行任务");
            task.run();
            return;
        }

        UserContext originalContext = null;
        try {
            // 保存原有上下文
            originalContext = UserContextHolder.getContext();
        } catch (Exception e) {
            log.debug("获取原有用户上下文失败: {}", e.getMessage());
        }

        try {
            // 设置指定用户的上下文
            UserContext userContext = UserContextHolder.getContext(userId);
            if (userContext != null) {
                UserContextHolder.setContext(userContext, false);
                log.debug("已设置用户上下文，用户ID: {}", userId);
            } else {
                log.warn("无法获取用户ID为 {} 的上下文信息", userId);
            }
            
            // 执行任务
            task.run();
        } catch (Exception e) {
            log.error("执行任务时发生异常", e);
            throw e;
        } finally {
            // 恢复原有上下文
            try {
                if (originalContext != null) {
                    UserContextHolder.setContext(originalContext, false);
                } else {
                    UserContextHolder.clearContext();
                }
            } catch (Exception e) {
                log.warn("恢复用户上下文失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 在非 Web 上下文中执行需要用户身份的操作（带返回值）
     * 
     * @param userId 用户ID
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果
     */
    public static <T> T executeWithUserContext(Long userId, java.util.function.Supplier<T> task) {
        if (userId == null) {
            log.warn("用户ID为空，将以系统身份执行任务");
            return task.get();
        }

        UserContext originalContext = null;
        try {
            // 保存原有上下文
            originalContext = UserContextHolder.getContext();
        } catch (Exception e) {
            log.debug("获取原有用户上下文失败: {}", e.getMessage());
        }

        try {
            // 设置指定用户的上下文
            UserContext userContext = UserContextHolder.getContext(userId);
            if (userContext != null) {
                UserContextHolder.setContext(userContext, false);
                log.debug("已设置用户上下文，用户ID: {}", userId);
            } else {
                log.warn("无法获取用户ID为 {} 的上下文信息", userId);
            }
            
            // 执行任务并返回结果
            return task.get();
        } catch (Exception e) {
            log.error("执行任务时发生异常", e);
            throw e;
        } finally {
            // 恢复原有上下文
            try {
                if (originalContext != null) {
                    UserContextHolder.setContext(originalContext, false);
                } else {
                    UserContextHolder.clearContext();
                }
            } catch (Exception e) {
                log.warn("恢复用户上下文失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 以系统管理员身份执行任务
     * 
     * @param task 要执行的任务
     */
    public static void executeAsAdmin(Runnable task) {
        // 通常系统管理员的ID为1，你可以根据实际情况调整
        executeWithUserContext(1L, task);
    }

    /**
     * 以系统管理员身份执行任务（带返回值）
     * 
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果
     */
    public static <T> T executeAsAdmin(java.util.function.Supplier<T> task) {
        // 通常系统管理员的ID为1，你可以根据实际情况调整
        return executeWithUserContext(1L, task);
    }
}
