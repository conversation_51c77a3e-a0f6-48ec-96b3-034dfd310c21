package top.continew.admin.job;

import top.continew.admin.biz.model.resp.SalesDailyStatsResp;

import java.util.List;

/**
 * 商务统计格式化工具类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025/08/04
 */
public class SalesStatisticsFormatter {

    /**
     * 方案一：表格式布局（推荐）
     */
    public static String formatAsTable(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return "📊 昨日商务统计\n" +
                   "━━━━━━━━━━━━━━━━━━━━\n" +
                   "暂无数据";
        }

        StringBuilder sb = new StringBuilder();
        
        // 美化的表头
        sb.append("📊 昨日商务统计\n");
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");
        sb.append("👤 商务人员    📈 客咨数据\n");
        sb.append("────────────────────\n");

        for (SalesDailyStatsResp stat : statsList) {
            String name = stat.getName();
            
            // 处理名字长度，中文字符按2个字符计算
            String displayName = formatChineseName(name, 8);
            
            // 格式化数据，使用更直观的显示方式
            String dataString = String.format("总:%d 微:%d TG:%d",
                    stat.getTotalCount(),
                    stat.getWechatCount(),
                    stat.getTelegramCount());

            // 使用更美观的格式
            sb.append(String.format("%-12s %s\n", displayName, dataString));
        }
        
        sb.append("────────────────────");
        return sb.toString();
    }

    /**
     * 方案二：卡片式布局
     */
    public static String formatAsCards(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return "📊 昨日商务统计\n\n🔍 暂无数据";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("📊 昨日商务统计\n\n");

        for (int i = 0; i < statsList.size(); i++) {
            SalesDailyStatsResp stat = statsList.get(i);
            
            sb.append("▫️ ").append(stat.getName()).append("\n");
            sb.append("   📈 总计: ").append(stat.getTotalCount()).append(" 人\n");
            sb.append("   💬 微信: ").append(stat.getWechatCount()).append(" 人\n");
            sb.append("   📱 TG: ").append(stat.getTelegramCount()).append(" 人\n");
            
            if (i < statsList.size() - 1) {
                sb.append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 方案三：简洁列表式
     */
    public static String formatAsSimpleList(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return "📊 昨日商务统计\n━━━━━━━━━━━━━━━━━━━━\n暂无数据";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("📊 昨日商务统计\n");
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");

        for (SalesDailyStatsResp stat : statsList) {
            String name = formatChineseName(stat.getName(), 6);
            sb.append("• ").append(name).append(" → ");
            sb.append(stat.getTotalCount()).append("(");
            sb.append(stat.getWechatCount()).append("/");
            sb.append(stat.getTelegramCount()).append(")\n");
        }

        return sb.toString();
    }

    /**
     * 方案四：排行榜式
     */
    public static String formatAsRanking(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return "🏆 昨日商务排行榜\n━━━━━━━━━━━━━━━━━━━━\n暂无数据";
        }

        // 按总数排序
        List<SalesDailyStatsResp> sortedList = statsList.stream()
                .sorted((a, b) -> Integer.compare(b.getTotalCount(), a.getTotalCount()))
                .toList();

        StringBuilder sb = new StringBuilder();
        sb.append("🏆 昨日商务排行榜\n");
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");

        String[] medals = {"🥇", "🥈", "🥉"};
        
        for (int i = 0; i < sortedList.size(); i++) {
            SalesDailyStatsResp stat = sortedList.get(i);
            String rank = i < 3 ? medals[i] : String.format("%2d.", i + 1);
            String name = formatChineseName(stat.getName(), 6);
            
            sb.append(rank).append(" ").append(name);
            sb.append(" - ").append(stat.getTotalCount()).append("人");
            sb.append(" (微:").append(stat.getWechatCount());
            sb.append(" TG:").append(stat.getTelegramCount()).append(")\n");
        }

        return sb.toString();
    }

    /**
     * 方案五：数据看板式
     */
    public static String formatAsDashboard(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return "📊 商务数据看板\n━━━━━━━━━━━━━━━━━━━━\n暂无数据";
        }

        // 计算总计
        int totalAll = statsList.stream().mapToInt(SalesDailyStatsResp::getTotalCount).sum();
        int totalWechat = statsList.stream().mapToInt(SalesDailyStatsResp::getWechatCount).sum();
        int totalTg = statsList.stream().mapToInt(SalesDailyStatsResp::getTelegramCount).sum();

        StringBuilder sb = new StringBuilder();
        sb.append("📊 商务数据看板\n");
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");
        sb.append("📈 总览: ").append(totalAll).append("人 ");
        sb.append("(微:").append(totalWechat).append(" TG:").append(totalTg).append(")\n");
        sb.append("────────────────────\n");

        for (SalesDailyStatsResp stat : statsList) {
            String name = formatChineseName(stat.getName(), 5);
            double percentage = totalAll > 0 ? (double) stat.getTotalCount() / totalAll * 100 : 0;
            
            sb.append("▪ ").append(name).append(": ");
            sb.append(stat.getTotalCount()).append("人 ");
            sb.append(String.format("(%.1f%%)", percentage)).append("\n");
        }

        return sb.toString();
    }

    /**
     * 格式化中文名字，考虑中文字符宽度
     */
    private static String formatChineseName(String name, int maxLength) {
        if (name == null) return "";
        
        if (name.length() <= maxLength) {
            return name;
        }
        
        return name.substring(0, maxLength) + "..";
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        List<SalesDailyStatsResp> stats = List.of(
                new SalesDailyStatsResp("张三", 5, 3, 2),
                new SalesDailyStatsResp("李四", 3, 1, 2),
                new SalesDailyStatsResp("王五", 1, 1, 0),
                new SalesDailyStatsResp("一个很长的名字", 7, 2, 5),
                new SalesDailyStatsResp("短", 2, 1, 1)
        );

        System.out.println("=== 方案一：表格式 ===");
        System.out.println(formatAsTable(stats));
        System.out.println("\n=== 方案二：卡片式 ===");
        System.out.println(formatAsCards(stats));
        System.out.println("\n=== 方案三：简洁列表 ===");
        System.out.println(formatAsSimpleList(stats));
        System.out.println("\n=== 方案四：排行榜 ===");
        System.out.println(formatAsRanking(stats));
        System.out.println("\n=== 方案五：数据看板 ===");
        System.out.println(formatAsDashboard(stats));
    }
}