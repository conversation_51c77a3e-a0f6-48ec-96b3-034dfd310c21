package top.continew.admin.job;

import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.event.BusinessTelegramMessageEvent;
import top.continew.admin.biz.model.resp.SalesDailyStatsResp;
import top.continew.admin.biz.service.crm.SalesDailyDataService;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class SalesJob {

    private final SalesDailyDataService salesDailyDataService;

    @Value("${businessTelegram.chatId}")
    private Long chatId;


    @JobExecutor(name = "sendPreviousDaySalesStatistics")
    public void sendPreviousDaySalesStatistics() {
        List<SalesDailyStatsResp> resp = salesDailyDataService.getPreviousDaySalesStatistics();



        String msg = formatAsTable(resp);
        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                .chatId(chatId)
                .text(msg)
                .build()));
    }


    public static String formatAsTable(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return """
                    📊 昨日商务统计
                    ━━━━━━━━━━━━━━━━━━━━
                    暂无数据""";
        }

        StringBuilder sb = new StringBuilder();

        // 美化的表头
        sb.append("📊 昨日商务统计\n");
        sb.append("━━━━━━━━━━━━━━━━━━━━\n");
        sb.append("👤 商务人员    📈 客咨数据\n");
        sb.append("────────────────────\n");

        for (SalesDailyStatsResp stat : statsList) {
            String name = stat.getName();

            // 处理名字长度，中文字符按2个字符计算
            String displayName = formatChineseName(name);

            // 格式化数据，使用更直观的显示方式
            String dataString = String.format("总:%d 微:%d TG:%d",
                    stat.getTotalCount(),
                    stat.getWechatCount(),
                    stat.getTelegramCount());

            // 使用更美观的格式
            sb.append(String.format("%-12s %s\n", displayName, dataString));
        }

        sb.append("────────────────────");
        return sb.toString();
    }

    /**
     * 格式化中文名字，考虑中文字符宽度
     */
    private static String formatChineseName(String name) {
        if (name == null) return "";

        if (name.length() <= 8) {
            return name;
        }
        return name.substring(0, 8) + "..";
    }

}