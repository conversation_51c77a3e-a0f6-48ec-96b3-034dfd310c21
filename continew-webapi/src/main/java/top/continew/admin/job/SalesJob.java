package top.continew.admin.job;

import cn.hutool.extra.spring.SpringUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.event.BusinessTelegramMessageEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.model.resp.SalesDailyStatsResp;
import top.continew.admin.biz.service.crm.SalesDailyDataService;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class SalesJob {

    private final SalesDailyDataService salesDailyDataService;

    @Value("${businessTelegram.chatId}")
    private Long chatId;


    @JobExecutor(name = "sendPreviousDaySalesStatistics")
    public void sendPreviousDaySalesStatistics() {
        List<SalesDailyStatsResp> resp = salesDailyDataService.getPreviousDaySalesStatistics();
        String msg = formatSalesStatisticsFixedNameLength(resp);

        SpringUtil.publishEvent(new BusinessTelegramMessageEvent(SendMessage.builder()
                .chatId(chatId)
                .text(msg)
                .build()));
    }


    public static String formatSalesStatisticsFixedNameLength(List<SalesDailyStatsResp> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            // 如果没有数据，同样处理表头，保持一致的固定长度
            return String.format("%-15s", "商务人员") + "客咨数量\n\n暂无数据";
        }

        StringBuilder sb = new StringBuilder();
        // 表头也进行相同处理：将“商务人员”左对齐并填充到15个字符长度
        sb.append(String.format("%-15s", "商务人员")).append("客咨数量\n\n");

        final int FIXED_NAME_LENGTH = 15; // 定义固定名称长度为15个字符

        for (SalesDailyStatsResp stat : statsList) {
            String name = stat.getName();
            String formattedName;

            if (name.length() > FIXED_NAME_LENGTH) {
                // 如果名字长度超过15个字符，则截断
                formattedName = name.substring(0, FIXED_NAME_LENGTH);
            } else {
                // 如果名字长度不足15个字符，则右侧填充空格
                formattedName = String.format("%-" + FIXED_NAME_LENGTH + "s", name);
            }

            // 拼接客咨数量：总数/微信数/Telegram数
            String countString = String.format("%d/%d/%d",
                    stat.getTotalCount(),
                    stat.getWechatCount(),
                    stat.getTelegramCount());

            // 名字部分和数量部分直接拼接，每行后跟两个换行符
            sb.append(formattedName).append(countString).append("\n");
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        // 模拟从 service 层获取到的数据
        List<SalesDailyStatsResp> stats = List.of(
                new SalesDailyStatsResp("张三", 5, 3, 2),
                new SalesDailyStatsResp("李四", 3, 1, 2),
                new SalesDailyStatsResp("王五", 1, 1, 0),
                new SalesDailyStatsResp("一个很长的名字用来测试截断", 7, 2, 5), // 超过15个字符 (25个字符)
                new SalesDailyStatsResp("短", 2, 1, 1) // 短于15个字符
        );

        String formattedOutput = formatSalesStatisticsFixedNameLength(stats);
        System.out.println(formattedOutput);
    }

}